<template>
  <div class="layout">
    <a-layout v-show="!isShowGuide">
      <a-layout-header class="header">
        <div class="header-content">
          <a-image
            width="100%"
            :preview="false"
            src="https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/banner.jpg"
            title="丁庄红色记忆馆"
            class="header-image"
          />
          <div class="header-overlay">
            <h1 class="header-title">丁庄红色记忆馆</h1>
            <p class="header-subtitle">传承红色基因 · 弘扬革命精神</p>
          </div>
        </div>
      </a-layout-header>

      <!-- 语音播放 -->
      <transition name="voice-slide" appear>
        <VoicePlayback :playData="playData" style="z-index: 9;"/>
      </transition>

      <!-- 主体 -->
      <div class="main-container">
        <div class="sidebar" :class="{ 'mobile-sidebar': isMobile }">
          <div class="sidebar-header">
            <h3 class="sidebar-title">展馆导航</h3>
            <button v-if="isMobile" class="mobile-menu-toggle" @click="toggleMobileMenu">
              <span class="hamburger-line"></span>
              <span class="hamburger-line"></span>
              <span class="hamburger-line"></span>
            </button>
          </div>
          <div class="menu-scrollable" :class="{ 'mobile-menu-open': showMobileMenu }">
            <div v-for="(item,index) in menuData"
                 class="menu-item"
                 :class="{'selected' : index == currIndex}"
                 @click="handleClick(index)">
                <div class="menu-item-image">
                  <img :src="item.imgUrl" alt="" @load="onImageLoad" @error="onImageError">
                </div>
                <span class="menu-item-text">{{ item.title }}</span>
            </div>
          </div>
        </div>

        <div class="content-area">
          <div class="content-header">
            <h2 class="content-title">{{ menuData[currIndex].title }}</h2>
            <div class="content-divider"></div>
          </div>

          <transition-group name="card-list" tag="div" class="content-grid">
            <div v-for="(item, idx) in menuData[currIndex].children"
                 :key="`${currIndex}_${idx}`"
                 class="content-card"
                 :class="{'content-selected' : currIndex + '_' +idx == currPlayIndex}"
                 @click="hanlePlay(idx)"
                 :style="{ '--delay': idx * 0.1 + 's' }">
              <div class="card-image-wrapper">
                <a-image
                  width="100%"
                  height="120px"
                  :preview="false"
                  :src="item.imgUrl"
                  :title="item.title"
                  class="card-image"
                />
                <div class="card-overlay">
                  <div class="play-icon">
                    <icon-play-arrow-fill :size="24" style="color: white;"/>
                  </div>
                </div>
              </div>
              <div class="card-content">
                <h4 class="card-title">{{ item.title }}</h4>
                <p class="card-description">{{ item.description }}</p>
              </div>
            </div>
          </transition-group>
        </div>
      </div>

      <!-- 轮播图 -->
      <div class="carousel-section">
        <div class="section-header">
          <h3 class="section-title">精彩瞬间</h3>
          <div class="section-divider"></div>
        </div>
        <div class="carousel-wrapper">
          <a-carousel
            :style="{
              width: '100%',
              height: '280px',
            }"
            :auto-play="true"
            indicator-type="dot"
            show-arrow="hover"
            class="custom-carousel"
          >
            <a-carousel-item v-for="image in images" :key="image">
              <div class="carousel-item">
                <img :src="image" alt="历史照片" class="carousel-image"/>
                <div class="carousel-overlay"></div>
              </div>
            </a-carousel-item>
          </a-carousel>
        </div>
      </div>

    </a-layout>

    <!-- 导览图 -->
    <div class="guide" v-show="!isShowGuide">
      <div class="button" id="scrollButton" @click="handleGuide">
        <img
          alt="avatar"
          src="https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/%E5%AF%BC%E8%A7%88.png"
        />
        <span>导览图</span>
      </div>
    </div>
    <!-- 图片 -->
    <div class="map" v-show="isShowGuide" :class="{'rotate-down': isShowGuide, 'rotate-up': !isShowGuide}">
      <p class="tips">
        <span style="font-size: 20px;">导览图</span>
        <br />
        <span>请横屏查看，点击图片后可进行放大缩小</span>
      </p>
      <a-image
        style="height: 100vh;"
        width="100%"
        height="100%"
        src="https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/%E5%AF%BC%E8%A7%88%E5%9B%BE.jpg"
      />
      <a-button class="button" style="transform: rotate(90deg);" type="primary" status="danger" @click="handleGuide">返回</a-button>

    </div>
  </div>
</template>

<script>
import VoicePlayback from '@/components/VoicePlayback.vue'
import { IconPlayArrowFill } from '@arco-design/web-vue/es/icon';

export default {
  components: {
    VoicePlayback,
    IconPlayArrowFill,
  },
  data() {
    return {
      playData: {},
      // 当前选择
      currIndex: 0,
      // 当前播放
      currPlayIndex: '0_0',
      // 导航数据
      menuData: [
        {
          title: '欢迎词',
          imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/001.jpg',
          // 子数据
          children: [
            {
              title: '欢迎词',
              description: '红色历史与振兴',
              imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/001.jpg',
              mp3Url: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/MP3/%E6%AC%A2%E8%BF%8E%E8%AF%8D.mp3',
            },
          ]
        },
        {
          title: '场馆',
          imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/002.png',
          // 子数据
          children: [
            {
              title: '第一展厅',
              description: '红色武装卫家园',
              imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/003.jpg',
              mp3Url: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/MP3/%E7%AC%AC%E4%B8%80%E5%B1%95%E5%8C%BA.mp3',
            },
            {
              title: '第二展厅',
              description: '红色政权定江山',
              imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/004.jpg',
              mp3Url: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/MP3/%E7%AC%AC%E4%BA%8C%E5%B1%95%E5%8C%BA.mp3',
            },
            {
              title: '第三展厅',
              description: '红色英名耀鲁南',
              imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/005.jpg',
              mp3Url: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/MP3/%E7%AC%AC%E4%B8%89%E5%B1%95%E5%8C%BA.mp3',
            },
            {
              title: '第四展厅',
              description: '红色沃土换新颜',
              imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/006.jpg',
              mp3Url: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/MP3/%E7%AC%AC%E5%9B%9B%E5%B1%95%E5%8C%BA.mp3',
            },
          ]
        },
      ],
      // 轮播数据
      images: [
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/012.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/011.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/010.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/009.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/008.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/007.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/006.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/005.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/004.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/003.jpg',
      ],
      // 当前屏幕宽度
      containerWidth: '100%',
      // 是否显示导览图
      isShowGuide: false,
      // 移动端相关
      isMobile: false,
      showMobileMenu: false
    }
  },
  computed: {
    imageWidthStyle() {
      // 获取当前容器的宽度
      const containerWidth = document.querySelector('#app').clientWidth;
      this.containerWidth = containerWidth;
      // 减去200px
      const calculatedWidth = `${containerWidth - 180}px`;
      return calculatedWidth;
    },
  },
  created() {
    this.playData = this.menuData[this.currIndex].children[0]
    this.checkMobile()
  },

  mounted() {
    window.addEventListener('resize', this.checkMobile)
  },

  beforeUnmount() {
    window.removeEventListener('resize', this.checkMobile)
  },
  // ...
  methods: {
    handleClick(index) {
      this.currIndex = index
      // 移动端点击后关闭菜单
      if (this.isMobile) {
        this.showMobileMenu = false
      }
    },

    // 播放
    hanlePlay(index) {
      this.currPlayIndex = this.currIndex + '_'+ index
      this.playData = this.menuData[this.currIndex].children[index]
    },

    // 导览图显示/隐藏
    handleGuide() {
      this.isShowGuide = !this.isShowGuide;
    },

    // 检查是否为移动端
    checkMobile() {
      this.isMobile = window.innerWidth <= 768
      if (!this.isMobile) {
        this.showMobileMenu = false
      }
    },

    // 切换移动端菜单
    toggleMobileMenu() {
      this.showMobileMenu = !this.showMobileMenu
    },

    // 图片加载完成
    onImageLoad(event) {
      event.target.style.opacity = '1'
    },

    // 图片加载失败
    onImageError(event) {
      event.target.style.opacity = '0.5'
      console.warn('图片加载失败:', event.target.src)
    }
  },
}
</script>


<style scoped>
/* 头部样式 */
.header {
  position: relative;
  padding: 0;
  height: auto;
  background: transparent;
}

.header-content {
  position: relative;
  overflow: hidden;
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.header-image {
  display: block;
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.6) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-sm) 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.header-subtitle {
  font-size: 1rem;
  margin: 0;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 主体布局 */
.main-container {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  min-height: 60vh;
}

/* 侧边栏 */
.sidebar {
  width: 180px;
  flex-shrink: 0;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.sidebar-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  padding: 0 var(--spacing-sm);
}

/* 移动端菜单切换按钮 */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.hamburger-line {
  width: 100%;
  height: 3px;
  background: var(--primary-red);
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* 移动端侧边栏 */
.mobile-sidebar .mobile-menu-toggle {
  display: flex;
}

.mobile-sidebar .menu-scrollable {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-card);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-heavy);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.mobile-sidebar .menu-scrollable.mobile-menu-open {
  display: block;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 内容区域 */
.content-area {
  flex: 1;
}

.content-header {
  margin-bottom: var(--spacing-lg);
}

.content-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-red);
  margin: 0 0 var(--spacing-sm) 0;
}

.content-divider {
  width: 60px;
  height: 3px;
  background: var(--gradient-red);
  border-radius: 2px;
}

/* 导航按钮样式 */
.guide {
  position: relative;
}

.guide .button {
  position: fixed;
  z-index: 100;
  top: 60%;
  right: var(--spacing-md);
  background: var(--bg-card);
  width: 70px;
  height: 85px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-large);
  flex-direction: column;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-medium);
  border: 2px solid var(--primary-red-light);
}

.guide .button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.guide .button img {
  width: 45px;
  height: 45px;
}

.guide .button span {
  font-size: 0.75rem;
  color: var(--primary-red);
  font-weight: 500;
  margin-top: var(--spacing-xs);
}
/* 菜单项样式 */
.menu-scrollable {
  max-height: 55vh;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.menu-scrollable::-webkit-scrollbar {
  display: none;
}

.menu-item {
  margin-bottom: var(--spacing-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-card);
  border-radius: var(--radius-large);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: var(--shadow-light);
}

.menu-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-red-light);
}

.menu-item-image {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-medium);
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
}

.menu-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.menu-item-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  text-align: center;
}

.menu-item.selected {
  background: var(--gradient-red);
  color: var(--text-white);
  border-color: var(--primary-red-dark);
  box-shadow: var(--shadow-red);
}

.menu-item.selected .menu-item-text {
  color: var(--text-white);
  font-weight: 600;
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.content-card {
  background: var(--bg-card);
  border-radius: var(--radius-large);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-light);
  border: 2px solid transparent;
}

.content-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-red-light);
}

.card-image-wrapper {
  position: relative;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.content-card:hover .card-image {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.content-card:hover .card-overlay {
  opacity: 1;
}

.play-icon {
  width: 48px;
  height: 48px;
  background: var(--primary-red);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.content-card:hover .play-icon {
  transform: scale(1.1);
}

.card-content {
  padding: var(--spacing-md);
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.card-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

/* 地图样式 */
.map {
  position: relative;
}

.map .tips {
  transform: rotate(90deg);
  writing-mode: vertical-rl;
  text-orientation: upright;
  display: inline-block;
  position: absolute;
  z-index: 100;
  right: 105px;
  top: -90px;
  color: var(--text-primary);
  font-weight: 500;
}

.map img {
  max-width: 100%;
}

.map .button {
  position: absolute;
  bottom: 70px;
  left: 10px;
}

/* 选中状态动画 */
.content-selected {
  position: relative;
  border-color: var(--primary-red) !important;
  box-shadow: var(--shadow-red) !important;
  animation: selectedPulse 2s ease-in-out infinite;
}

@keyframes selectedPulse {
  0%, 100% {
    box-shadow: var(--shadow-red);
  }
  50% {
    box-shadow: 0 4px 20px rgba(211, 47, 47, 0.5);
  }
}

/* 轮播图样式 */
.carousel-section {
  margin: var(--spacing-xl) var(--spacing-lg);
}

.section-header {
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-red);
  margin: 0 0 var(--spacing-sm) 0;
}

.section-divider {
  width: 80px;
  height: 3px;
  background: var(--gradient-red);
  border-radius: 2px;
  margin: 0 auto;
}

.carousel-wrapper {
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.carousel-item {
  position: relative;
  height: 280px;
  overflow: hidden;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.3) 0%,
    transparent 50%
  );
}

/* 动画效果 */
.rotate-down {
  transform-origin: 50% 0%;
  animation: rotateDown 0.8s ease-out;
}

.rotate-up {
  transform-origin: 50% 100%;
  animation: rotateUp 0.8s ease-out;
}

@keyframes rotateDown {
  0% {
    transform: rotateX(90deg);
    opacity: 0.2;
  }
  100% {
    transform: rotateX(0deg);
    opacity: 1;
  }
}

@keyframes rotateUp {
  0% {
    transform: rotateX(-90deg);
    opacity: 0.2;
  }
  100% {
    transform: rotateX(0deg);
    opacity: 1;
  }
}

/* 过渡动画 */
.voice-slide-enter-active {
  transition: all 0.6s ease-out;
}

.voice-slide-enter-from {
  transform: translateY(-100%);
  opacity: 0;
}

.card-list-enter-active {
  transition: all 0.6s ease-out;
  transition-delay: var(--delay);
}

.card-list-leave-active {
  transition: all 0.3s ease-in;
}

.card-list-enter-from {
  transform: translateY(30px);
  opacity: 0;
}

.card-list-leave-to {
  transform: translateY(-30px);
  opacity: 0;
}

.card-list-move {
  transition: transform 0.5s ease;
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-card {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: var(--delay, 0s);
  animation-fill-mode: both;
}

/* 悬停效果增强 */
.menu-item {
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.menu-item:hover::before {
  left: 100%;
}

.content-card {
  position: relative;
  overflow: hidden;
}

.content-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.content-card:hover::after {
  transform: translateX(100%);
}

/* 脉冲效果 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.play-icon {
  animation: pulse 2s ease-in-out infinite;
}

.content-card:hover .play-icon {
  animation: none;
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    position: relative;
  }

  .sidebar {
    width: 100%;
    position: relative;
  }

  .sidebar.mobile-sidebar .menu-scrollable:not(.mobile-menu-open) {
    display: none;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .header-title {
    font-size: 1.5rem;
  }

  .header-subtitle {
    font-size: 0.9rem;
  }

  .carousel-section {
    margin: var(--spacing-lg) var(--spacing-md);
  }

  /* 移动端菜单项布局 */
  .mobile-sidebar .menu-item {
    flex-direction: row;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
  }

  .mobile-sidebar .menu-item-image {
    width: 50px;
    height: 50px;
    margin-right: var(--spacing-md);
    margin-bottom: 0;
  }

  .mobile-sidebar .menu-item-text {
    text-align: left;
  }

  /* 移动端动画优化 */
  .content-card {
    animation-duration: 0.4s;
  }

  .card-list-enter-active {
    transition-duration: 0.4s;
  }

  /* 导航按钮移动端优化 */
  .guide .button {
    width: 60px;
    height: 75px;
    right: var(--spacing-sm);
  }

  .guide .button img {
    width: 35px;
    height: 35px;
  }

  .guide .button span {
    font-size: 0.7rem;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  .header-title {
    font-size: 1.2rem;
  }

  .header-subtitle {
    font-size: 0.8rem;
  }

  .main-container {
    padding: var(--spacing-sm);
  }

  .content-grid {
    gap: var(--spacing-sm);
  }

  .card-content {
    padding: var(--spacing-sm);
  }

  .card-title {
    font-size: 1rem;
  }

  .card-description {
    font-size: 0.8rem;
  }
}


</style>