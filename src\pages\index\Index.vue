<template>
  <div class="layout">
    <a-layout v-show="!isShowGuide">
      <a-layout-header >

        <a-image
          width="100%"
          :preview="false"
          src="https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/banner.jpg"
          title="丁庄红色记忆馆"
        />

      </a-layout-header>

      <!-- 语音播放 -->
      <VoicePlayback :playData="playData" style="z-index: 9; height: 10vh;"/>

      <!-- 主体 -->
      <div class="main">
        <div class="menu-scrollable">
          <div v-for="(item,index) in menuData" class="menu-item" :class="{'selected' : index == currIndex}" @click="handleClick(index)">
              <img :src="item.imgUrl">
              <span>{{ item.title }}</span>
          </div>
        </div>
        <div class="content">
          <!-- <p class="title">{{ menuData[currIndex].title }}</p> -->

          <div class="content-scrollable">
            <div v-for="(item, idx) in menuData[currIndex].children" class="item" :class="{'content-selected' : currIndex + '_' +idx == currPlayIndex}" @click="hanlePlay(idx)">
              <a-image
                :width="imageWidthStyle"
                height="100px"
                :preview="false"
                :src="item.imgUrl"
                :title="item.title"
                :description="item.description"
              />
            </div>

          </div>

        </div>
      </div>

      <!-- 轮播图 -->
      <div class="carousel">
        <a-carousel
          :style="{
            width: '100%',
            height: '240px',
          }"
          :auto-play="true"
          indicator-type="dot"
          show-arrow="hover"
        >
          <a-carousel-item v-for="image in images">
            <img
              :src="image"
              :style="{
                width: '100%',
              }"
            />
          </a-carousel-item>
        </a-carousel>
      </div>

    </a-layout>

    <!-- 导览图 -->
    <div class="guide" v-show="!isShowGuide">
      <div class="button" id="scrollButton" @click="handleGuide">
        <img
          alt="avatar"
          src="https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/%E5%AF%BC%E8%A7%88.png"
        />
        <span>导览图</span>
      </div>
    </div>
    <!-- 图片 -->
    <div class="map" v-show="isShowGuide" :class="{'rotate-down': isShowGuide, 'rotate-up': !isShowGuide}">
      <p class="tips">
        <span style="font-size: 20px;">导览图</span>
        <br />
        <span>请横屏查看，点击图片后可进行放大缩小</span>
      </p>
      <a-image
        style="height: 100vh;"
        width="100%"
        height="100%"
        src="https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/%E5%AF%BC%E8%A7%88%E5%9B%BE.jpg"
      />
      <a-button class="button" style="transform: rotate(90deg);" type="primary" status="danger" @click="handleGuide">返回</a-button>

    </div>
  </div>
</template>

<script>
import VoicePlayback from '@/components/VoicePlayback.vue'

export default {
  components: {
    VoicePlayback,
  },
  data() {
    return {
      playData: {},
      // 当前选择
      currIndex: 0,
      // 当前播放
      currPlayIndex: '0_0',
      // 导航数据
      menuData: [
        {
          title: '欢迎词',
          imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/001.jpg',
          // 子数据
          children: [
            {
              title: '欢迎词',
              description: '红色历史与振兴',
              imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/001.jpg',
              mp3Url: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/MP3/%E6%AC%A2%E8%BF%8E%E8%AF%8D.mp3',
            },
          ]
        },
        {
          title: '场馆',
          imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/002.png',
          // 子数据
          children: [
            {
              title: '第一展厅',
              description: '红色武装卫家园',
              imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/003.jpg',
              mp3Url: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/MP3/%E7%AC%AC%E4%B8%80%E5%B1%95%E5%8C%BA.mp3',
            },
            {
              title: '第二展厅',
              description: '红色政权定江山',
              imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/004.jpg',
              mp3Url: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/MP3/%E7%AC%AC%E4%BA%8C%E5%B1%95%E5%8C%BA.mp3',
            },
            {
              title: '第三展厅',
              description: '红色英名耀鲁南',
              imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/005.jpg',
              mp3Url: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/MP3/%E7%AC%AC%E4%B8%89%E5%B1%95%E5%8C%BA.mp3',
            },
            {
              title: '第四展厅',
              description: '红色沃土换新颜',
              imgUrl: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/006.jpg',
              mp3Url: 'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/MP3/%E7%AC%AC%E5%9B%9B%E5%B1%95%E5%8C%BA.mp3',
            },
          ]
        },
      ],
      // 轮播数据
      images: [
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/012.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/011.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/010.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/009.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/008.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/007.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/006.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/005.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/004.jpg',
        'https://gaodux.oss-cn-qingdao.aliyuncs.com/jiangjie/IMAGE/003.jpg',
      ],
      // 当前屏幕宽度
      containerWidth: '100%',
      // 是否显示导览图
      isShowGuide: false
    }
  },
  computed: {
    imageWidthStyle() {
      // 获取当前容器的宽度
      const containerWidth = document.querySelector('#app').clientWidth;
      this.containerWidth = containerWidth;
      // 减去200px
      const calculatedWidth = `${containerWidth - 180}px`;
      return calculatedWidth;
    },
  },
  created() {
    this.playData = this.menuData[this.currIndex].children[0]
  },
  // ...
  methods: {
    handleClick(index) {
      this.currIndex = index
    },

    // 播放
    hanlePlay(index) {
      this.currPlayIndex = this.currIndex + '_'+ index
      this.playData = this.menuData[this.currIndex].children[index]
    },

    // 导览图显示/隐藏
    handleGuide() {
      this.isShowGuide = !this.isShowGuide;
    },
  },
}
</script>


<style scoped>
.carousel{
  margin: 20px;
}

.guide{
  position: relative;
  .button{
    position: fixed;
    z-index: 100;
    top: 60%;
    right: 5px;
    background-color: #fff;
    width: 65px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    flex-flow: column;
    transition: opacity 0.3s, right 0.3s; /* 添加过渡效果 */

    img{
      width: 45px;
      height: 45px;
    }
  }

}
.map{
  position: relative;

  .tips{
    transform: rotate(90deg);
    writing-mode: vertical-rl;
    text-orientation: upright;
    display: inline-block;
    position: absolute;
    z-index: 100;
    right: 105px;
    top: -90px;
  }
  img{
    max-width: 100%;
  }

  .button{
    position: absolute;
    bottom: 70px;
    left: 10px;
  }
}

.rotate-down {
  transform-origin: 50% 0%;
  animation: rotateDown 0.8s;
}
.rotate-up {
  transform-origin: 50% 100%;
  animation: rotateUp 0.8s;
}
@keyframes rotateDown {
  0% {
    transform: rotateX(90deg);
    opacity: 0.2;
  }
  100% {
    transform: rotateX(0deg);
    opacity: 1;
  }
}

@keyframes rotateUp {
  0% {
    transform: rotateX(-90deg);
    opacity: 0.2;
  }
  100% {
    transform: rotateX(0deg);
    opacity: 1;
  }
}

.menu-item{
  margin: 0.1rem 2rem 1rem 1rem;
  display: flex;
  flex-flow: column;
  align-items: center;
  padding: 10px;
  background-color: #f7f7f7;
  border-radius: 15px;
  width: 80px;
  height: 80px;

  img{
    width: 60px;
    height: 60px;
    border-radius: 10px;
    margin-bottom: 10px;
  }
}

.selected{
  background: #F40002;
  color: #fff;
  font-weight: bold;
  font-size: large;
  border-radius: 15px;
}

/* 隐藏所有浏览器的滚动条 */
.menu-scrollable {
  width: 150px;
  max-height: 55vh;
  overflow-y: scroll; /* 允许垂直滚动 */
  scrollbar-width: none; /* Firefox */
  &::-webkit-scrollbar { /* WebKit 浏览器 */
    display: none;
  }
}

/* Firefox专用 */
.menu-scrollable::-webkit-scrollbar {
  width: 0;
  height: 0;
}

/* WebKit 浏览器专用 */
.menu-scrollable {
  -ms-overflow-style: none;  /* IE 和 Edge */
  scrollbar-width: none;  /* Firefox */
}
.menu-scrollable::-webkit-scrollbar {
  display: none;  /* Chrome, Safari, and Opera */
}


.main{
  display: flex;

}
.content{

  .title{
    font-weight: bold;
  }

  .item{
    margin: 2px 10px 10px 0;
    border: 3px solid #F40002;
    border-radius: 5px;
  }
}



.content-selected {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
}

.content-selected::before,
.content-selected::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #dee649; /* 边框颜色 */
  animation: scrollBorder 5s linear infinite;
}

.content-selected::before {
  top: 0;
  left: 0;
}

.content-selected::after {
  top: 0;
  right: 0;
}

/* 添加另外两个角 */
.content-selected::nth-child(3)::before {
  bottom: 0;
  left: 0;
  animation-delay: 1.25s; /* 延迟，让动画错开 */
}

.content-selected::nth-child(3)::after {
  bottom: 0;
  right: 0;
  animation-delay: 2.5s; /* 延迟，让动画错开 */
}

@keyframes scrollBorder {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.5); /* 在中间点增加边框大小 */
  }
  100% {
    transform: scale(1);
  }
}



/* 隐藏所有浏览器的滚动条 */
.content-scrollable {
  max-height: 55vh;
  overflow-y: scroll; /* 允许垂直滚动 */
  scrollbar-width: none; /* Firefox */
  &::-webkit-scrollbar { /* WebKit 浏览器 */
    display: none;
  }
}


</style>