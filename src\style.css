:root {
  /* 字体系统 */
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* 红色主题色彩系统 */
  --primary-red: #D32F2F;
  --primary-red-dark: #B71C1C;
  --primary-red-light: #F44336;
  --primary-red-lighter: #FFEBEE;

  /* 渐变色 */
  --gradient-red: linear-gradient(135deg, #D32F2F 0%, #F44336 100%);
  --gradient-red-dark: linear-gradient(135deg, #B71C1C 0%, #D32F2F 100%);
  --gradient-gold: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);

  /* 中性色 */
  --text-primary: #212121;
  --text-secondary: #757575;
  --text-light: #BDBDBD;
  --text-white: #FFFFFF;

  /* 背景色 */
  --bg-primary: #FAFAFA;
  --bg-secondary: #F5F5F5;
  --bg-card: #FFFFFF;
  --bg-overlay: rgba(0, 0, 0, 0.6);

  /* 阴影 */
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-red: 0 4px 16px rgba(211, 47, 47, 0.3);

  /* 边框半径 */
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 16px;
  --radius-xl: 24px;

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  color-scheme: light;
  color: var(--text-primary);
  background-color: var(--bg-primary);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  overflow: hidden;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

::-webkit-scrollbar {
  display: none;  /* Chrome, Safari, and Opera */
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  background: var(--bg-primary);
}

/* 移动端优化 */
@media (max-width: 480px) {
  :root {
    --spacing-xs: 2px;
    --spacing-sm: 4px;
    --spacing-md: 8px;
    --spacing-lg: 12px;
    --spacing-xl: 16px;
    --spacing-2xl: 24px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .menu-item,
  .content-card,
  .guide .button {
    transform: none !important;
  }

  .menu-item:active,
  .content-card:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease;
  }
}
