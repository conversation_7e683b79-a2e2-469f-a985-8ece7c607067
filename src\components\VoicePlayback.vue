<template>
    <div class="voice-playback">
        <div class="voice-playback-left">
            <!-- 头像 -->
            <a-avatar :size="64" class="avatar">
                <img alt="avatar" :style="{ transform: `rotate(${rotationAngle}deg)` }" :src="playData.imgUrl" />
            </a-avatar>
            <icon-play-arrow-fill v-if="!isPlaying" @click="togglePlayPause" class="play-bnt" :size="42" style="color: #fff;"/>
            <icon-pause v-if="isPlaying" @click="togglePlayPause" class="play-bnt" :size="42" style="color: #fff;"/>
        </div>
        <!-- 进度条及控制部分 -->
        <div class="voice-playback-right">
            <audio ref="audio" :src="playData.mp3Url" @loadedmetadata="onMetadataLoaded" @timeupdate="onTimeUpdate" autoplay></audio>
            <div class="voice-playback-title">{{ playData.title }}</div>
            <div class="voice-playback-progress">
                <span class="voice-playback-progress-time">{{ currentTime }}</span>
                <a-slider v-model="progress" :min="0" :max="100" :step="1" :default-value="0" :show-tooltip="false"/>
                <span class="voice-playback-progress-time">{{ durationTime }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import { IconPlayArrowFill, IconPause } from '@arco-design/web-vue/es/icon';
export default {
    components: { IconPlayArrowFill, IconPause },
    props: {
        playData: Object,
    },
    data() {
        return {
            // 音频时长
            duration: 0,
            durationTime: '00:00',
            // 当前播放进度
            currentTime: '00:00',
            // 初始进度
            progress: 0,
            // 是否播放
            isPlaying: false,
            rotationAngle: 0, // 当前旋转角度
            intervalId: null, // 用于定时更新旋转角度
        };
    },
    // 深度监听
    watch: {
        playData: {
            handler(newVal, oldVal) {
                // console.log("newVal", newVal);
                // console.log("oldVal", oldVal);
                // 初始化状态
                this.progress = 0;
                this.currentTime = '00:00';
                this.isPlaying = false;
                // 清除定时器
                clearInterval(this.intervalId);

                this.title = newVal.title;
                this.mp3Url = newVal.mp3Url;
                this.imgUrl = newVal.imgUrl;
                // 自动播放
                this.togglePlayPause();
            },
            deep: true
        },
    },

    methods: {
        // 监听音频时长
        onMetadataLoaded(event) {
            const audioElement = event.target;
            let duration = Math.floor(audioElement.duration);
            this.duration = duration;
            // 将秒转换为分钟和秒
            const minutes = Math.floor(duration / 60);
            const seconds = Math.floor(duration % 60);
            this.durationTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
        },

        // 监听音频播放进度
        onTimeUpdate(event) {
            let currentTime = Math.floor(event.target.currentTime);
            // 进度条处理 0-100 整数
            this.progress = Math.floor((currentTime / this.duration) * 100);
            if (this.progress == 100) {
                this.togglePlayPause();
            }
            // 将秒转换为分钟和秒
            const minutes = Math.floor(currentTime / 60);
            const seconds = Math.floor(currentTime % 60);
            this.currentTime = `${minutes < 10 ? '0' : ''}${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
        },

        // 暂停与播放控制
        togglePlayPause() {
            if (this.isPlaying) {
                this.$refs.audio.pause();
                clearInterval(this.intervalId); // 清除定时器
            } else {
                this.$refs.audio.play();
                this.intervalId = setInterval(() => {
                    this.rotationAngle = (this.rotationAngle + 2) % 360; // 每秒旋转 9 度
                }, 100);
            }
            this.isPlaying = !this.isPlaying;
        },
    }
};
</script>

<style scoped>
.voice-playback {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: var(--spacing-lg);
    background: var(--gradient-red);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-red);
    position: relative;
    overflow: hidden;
}

.voice-playback::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.voice-playback-left {
    position: relative;
    margin-right: var(--spacing-md);
}

.avatar {
    position: relative;
    z-index: 1;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.play-bnt {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-bnt:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: translate(-50%, -50%) scale(1.1);
}

.voice-playback-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.voice-playback-title {
    color: var(--text-white);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.voice-playback-progress {
    display: flex;
    align-items: center;
    color: var(--text-white);
    gap: var(--spacing-md);
}

.voice-playback-progress-time {
    font-size: 0.9rem;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 隐藏 <audio> 元素 */
audio {
    display: none;
}

.rotating {
    animation: rotate 10s linear infinite;
}

.rotating-paused {
    animation-play-state: paused;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .voice-playback {
        margin: var(--spacing-md);
        padding: var(--spacing-md);
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .voice-playback-left {
        margin-right: 0;
    }

    .voice-playback-title {
        font-size: 1rem;
    }

    .voice-playback-progress {
        justify-content: center;
    }
}
</style>
