<template>
    <div class="voice-playback">
        <div class="voice-playback-left">
            <!-- 头像 -->
            <a-avatar :size="64" class="avatar">
                <img alt="avatar" :style="{ transform: `rotate(${rotationAngle}deg)` }" :src="playData.imgUrl" />
            </a-avatar>
            <icon-play-arrow-fill v-if="!isPlaying" @click="togglePlayPause" class="play-bnt" :size="42" style="color: #fff;"/>
            <icon-pause v-if="isPlaying" @click="togglePlayPause" class="play-bnt" :size="42" style="color: #fff;"/>
        </div>
        <!-- 进度条及控制部分 -->
        <div class="voice-playback-right">
            <audio ref="audio" :src="playData.mp3Url" @loadedmetadata="onMetadataLoaded" @timeupdate="onTimeUpdate" autoplay></audio>
            <div class="voice-playback-title">{{ playData.title }}</div>
            <div class="voice-playback-progress">
                <span class="voice-playback-progress-time">{{ currentTime }}</span>
                <a-slider v-model="progress" :min="0" :max="100" :step="1" :default-value="0" :show-tooltip="false"/>
                <span class="voice-playback-progress-time">{{ durationTime }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import { IconPlayArrowFill, IconPause } from '@arco-design/web-vue/es/icon';
export default {
    components: { IconPlayArrowFill, IconPause },
    props: {
        playData: Object,
    },
    data() {
        return {
            // 音频时长
            duration: 0,
            durationTime: '00:00',
            // 当前播放进度
            currentTime: '00:00',
            // 初始进度
            progress: 0,
            // 是否播放
            isPlaying: false,
            rotationAngle: 0, // 当前旋转角度
            intervalId: null, // 用于定时更新旋转角度
        };
    },
    // 深度监听
    watch: {
        playData: {
            handler(newVal, oldVal) {
                // console.log("newVal", newVal);
                // console.log("oldVal", oldVal);
                // 初始化状态
                this.progress = 0;
                this.currentTime = '00:00';
                this.isPlaying = false;
                // 清除定时器
                clearInterval(this.intervalId);

                this.title = newVal.title;
                this.mp3Url = newVal.mp3Url;
                this.imgUrl = newVal.imgUrl;
                // 自动播放
                this.togglePlayPause();
            },
            deep: true
        },
    },

    methods: {
        // 监听音频时长
        onMetadataLoaded(event) {
            const audioElement = event.target;
            let duration = Math.floor(audioElement.duration);
            this.duration = duration;
            // 将秒转换为分钟和秒
            const minutes = Math.floor(duration / 60);
            const seconds = Math.floor(duration % 60);
            this.durationTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
        },

        // 监听音频播放进度
        onTimeUpdate(event) {
            let currentTime = Math.floor(event.target.currentTime);
            // 进度条处理 0-100 整数
            this.progress = Math.floor((currentTime / this.duration) * 100);
            if (this.progress == 100) {
                this.togglePlayPause();
            }
            // 将秒转换为分钟和秒
            const minutes = Math.floor(currentTime / 60);
            const seconds = Math.floor(currentTime % 60);
            this.currentTime = `${minutes < 10 ? '0' : ''}${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
        },

        // 暂停与播放控制
        togglePlayPause() {
            if (this.isPlaying) {
                this.$refs.audio.pause();
                clearInterval(this.intervalId); // 清除定时器
            } else {
                this.$refs.audio.play();
                this.intervalId = setInterval(() => {
                    this.rotationAngle = (this.rotationAngle + 2) % 360; // 每秒旋转 9 度
                }, 100);
            }
            this.isPlaying = !this.isPlaying;
        },
    }
};
</script>

<style scoped>
    .voice-playback {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 1rem;
        background-color: #F40002;
        border-radius: 1rem;
        padding: 1rem;

        .voice-playback-left{
            position: relative;

            .avatar{
                position: relative;
                z-index: 1;
            }

            .play-bnt{
                position: absolute;
                top: 12px;
                left: 12px;
                z-index: 2;
            }


        }

        .voice-playback-right{
            flex: 1;
            margin-right: 10px;
            display: flex;
            flex-flow: column;

            .voice-playback-title{
                color: #fff;
                margin-left: 15px;
                margin-bottom: 5px;
                font-size: 16px;
                font-weight: bold;
            }

            .voice-playback-progress{
                display: flex;
                align-items: center;
                color: #fff;

                .voice-playback-progress-time{
                    margin: 0 15px;
                }
            }
        }
    }

    /* 隐藏 <audio> 元素 */
    audio {
        display: none;
    }

    .rotating {
        animation: rotate 10s linear infinite;
    }
    .rotating-paused {
        animation-play-state: paused;
    }


    @keyframes rotate {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }
</style>
